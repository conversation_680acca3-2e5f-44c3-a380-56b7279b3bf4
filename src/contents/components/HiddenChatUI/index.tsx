import React, { useRef, useEffect, forwardRef, useImperativeHandle, useState } from 'react'
import { init, log } from '@ht/xlog'
import { isPrd } from '@src/common/utils'
import ChatUI from '@ht/chatui-beta18'
import { getAllActions } from '@src/common/actions'
import { createConfig, defaultConfig } from '@/src/config/aiChatConfig'
import * as styles from './index.module.less'
import { getUserId } from '@src/common/utils/userConfigApi'

interface HiddenChatUIProps {
  selectedText?: string;
  textOperation?: string;
}

interface HiddenChatUIRef {
  chatContext: any;
  onSend: (type: string, content: string, options?: any, attachments?: any[]) => Promise<any>;
}


// Content Script环境的消息API适配
const createContentScriptMessageAPI = () => {
  return {
    success: (content: string) => {
      console.log('HiddenChatUI Success:', content)
      // 可以在这里添加页面通知逻辑
    },
    error: (content: string) => {
      console.error('HiddenChatUI Error:', content)
      // 可以在这里添加页面错误通知逻辑
    },
    warning: (content: string) => {
      console.warn('HiddenChatUI Warning:', content)
    },
    info: (content: string) => {
      console.info('HiddenChatUI Info:', content)
    }
  }
}


const HiddenChatUI = forwardRef<HiddenChatUIRef, HiddenChatUIProps>(({ selectedText, textOperation }, ref) => {
  const chatUiRef = useRef<any>(null)
  const actions = getAllActions()
  const [config, setConfig] = useState(defaultConfig)

  // 初始化日志
  const initLog = async () => {
    const userId = await getUserId()
    init({
      uuid: userId,
      from: 'HtscAiExtension_ContentScript',
      types: ['fetch', 'unhandledrejection', 'windowError'],
      myTrackConfig: {
        product_id: '366',
        product_name: 'Web开发平台',
        channel_env: isPrd ? 'prd_outer' : 'prd_outer_test',
      },
    })
  }

  useEffect(() => {
    const initializeComponent = async () => {
      // 初始化日志
      await initLog()

      // 异步加载配置
      try {
        const asyncConfig = await createConfig()
        setConfig(asyncConfig)
      } catch (error) {
        console.error('加载配置失败，使用默认配置:', error)
        // 已经使用默认配置，无需额外处理
      }
    }

    initializeComponent()
  }, [])


  // 日志上报函数
  const onReportLog = (params: any) => {
    const { id, page_id, page_title, btn_id, btn_title } = params
    if (id) {
      log({
        id,
        page_id,
        page_title,
        btn_id,
        btn_title,
      })
    }
  }

  // 暴露给父组件的方法
  useImperativeHandle(ref, () => ({
    get chatContext() {
      return chatUiRef.current?.chatContext
    },
    onSend: async (type: string, content: string, options?: any, attachments?: any[]) => {
      if (chatUiRef.current?.chatContext?.onSend) {
        console.log('执行onsend方法');

        return await chatUiRef.current.chatContext.onSend(type, content, {
          ...options,
          extendParams: {
            ...options.extendParams,
            hideConversation: true,
          }
        }, attachments)
      }
      throw new Error('ChatUI not initialized')
    }
  }), [])

  return (
    <div className={styles.hiddenChatUIContainer}>
      <ChatUI
        navbar={{
          showLogo: false,
          showCloseButton: false,
          title: '',
        }}
        historyConversation={{
          navbar: {
            showLogo: false,
            title: '聊天记录',
            showNewButton: false,
            showCloseButton: false,
            showReturnButton: true,
          },
        }}
        messageContainerConfig={{}}
        ref={chatUiRef}
        config={config}
        actions={actions}
        renderWelcome={() => null} // 不渲染欢迎页面
        onReportLog={onReportLog}
        inputOptions={{
          minRows: 2,
        }}
        renderFooterVersion={() => null} // 不渲染版本信息
        showStopAnswer={true}
        showToken={false}
        showHallucination={false}
      />
    </div>
  )
})

HiddenChatUI.displayName = 'HiddenChatUI'

export default HiddenChatUI
export type { HiddenChatUIRef }
